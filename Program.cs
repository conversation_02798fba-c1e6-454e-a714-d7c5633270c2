// File: Program.cs

using System.Net;
using System.Net.Sockets;
using System.Text;

class Program
{
    static async Task Main(string[] args)
    {
        // 1. Define the IP address and port to listen on.
        // IPAddress.Any would listen on all network interfaces,
        // but for this challenge, we'll stick to localhost.
        IPAddress ipAddress = IPAddress.Loopback;
        int port = 4221;

        // 2. Create a TcpListener instance.
        TcpListener server = new TcpListener(ipAddress, port);

        try
        {
            // 3. Start the listener. This binds the socket to the address
            //    and port and starts listening for incoming connections.
            server.Start();
            Console.WriteLine($"Server started. Listening on {ipAddress}:{port}");

            // 4. Enter an infinite loop to accept client connections.
            //    The server will run until the process is terminated.
            while (true)
            {
                // 5. Asynchronously accept a new TCP client connection.
                //    The 'await' keyword pauses execution here until a client connects.
                TcpClient client = await server.AcceptTcpClientAsync();
                Console.WriteLine("Client connected.");

                // In Stage 2, we handle the connection by sending a response.
                // We wrap the handling in a Task.Run to process it without blocking the loop.
                // Although not strictly necessary for this simple stage, it's good practice
                // for what comes next (concurrency).
                _ = Task.Run(() => HandleClient(client));
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An error occurred: {ex.Message}");
        }
        finally
        {
            // 6. Ensure the server stops listening if the loop exits.
            server.Stop();
        }
    }

    private static async Task HandleClient(TcpClient client)
    {
        try
        {
            await using NetworkStream stream = client.GetStream();

            // 1. Create a buffer to store the incoming request data.
            byte[] buffer = new byte[1024];
            int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);

            // 2. Convert the received bytes into a string.
            string requestString = Encoding.ASCII.GetString(buffer, 0, bytesRead);
            Console.WriteLine($"Received request:\n{requestString}");

            // 3. Parse the request line to extract the path.
            // The request line is the first line of the request.
            string[] requestLines = requestString.Split("\r\n");
            string requestLine = requestLines[0];
            string[] requestLineParts = requestLine.Split(' ');
            string path = requestLineParts[1];

            // 4. Prepare the response based on the path.
            string httpResponse;

            // 1. Add new routing logic for the /echo/ endpoint.
            if (path.StartsWith("/echo/"))
            {
                // 2. Extract the content from the path.
                string content = path.Substring("/echo/".Length);

                // 3. Construct the response with headers and a body.
                httpResponse = $"HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: {content.Length}\r\n\r\n{content}";
                Console.WriteLine($"Responding with '200 OK' and body '{content}' for path '{path}'.");
            }
            else if (path == "/")
            {
                httpResponse = "HTTP/1.1 200 OK\r\n\r\n";
                Console.WriteLine("Responding with '200 OK' for path '/'.");
            }
            else
            {
                httpResponse = "HTTP/1.1 404 Not Found\r\n\r\n";
                Console.WriteLine($"Responding with '404 Not Found' for path '{path}'.");
            }

            byte[] responseBytes = Encoding.ASCII.GetBytes(httpResponse);
            await stream.WriteAsync(responseBytes, 0, responseBytes.Length);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error handling client: {ex.Message}");
        }
        finally
        {
            client.Close();
            Console.WriteLine("Client disconnected.");
        }
    }
}
